@extends('layouts.admin')

@section('title', 'จัดการกิจกรรม')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-calendar-alt"></i> จัดการกิจกรรม
            </h1>
            <p class="text-muted mb-0">จัดการข้อมูลกิจกรรมทั้งหมด</p>
        </div>
        <a href="{{ route('admin.activities.create') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">กิจกรรมทั้งหมด</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $activities->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">เผยแพร่แล้ว</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $activities->where('is_published', true)->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">ร่าง</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $activities->where('is_published', false)->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-edit fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">หมวดหมู่</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ $categories->count() }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="card shadow mb-4" id="bulkActionsCard" style="display: none;">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <span class="me-3">
                    <strong id="selectedCount">0</strong> รายการที่เลือก
                </span>
                <button type="button" class="btn btn-danger btn-sm" onclick="bulkDelete()">
                    <i class="fas fa-trash"></i> ลบที่เลือก
                </button>
            </div>
        </div>
    </div>

    <!-- Activities Grid -->
    <div class="row" id="activitiesGrid">
        @forelse($activities as $activity)
            <div class="col-lg-4 col-md-6 col-12 mb-4 activity-item" 
                 data-activity-id="{{ $activity->id }}"
                 data-category-id="{{ $activity->category_id }}"
                 data-status="{{ $activity->is_published ? '1' : '0' }}">
                <div class="card shadow-sm border-0 h-100 activity-card" style="cursor: pointer;">
                    <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input class="form-check-input activity-checkbox" type="checkbox" value="{{ $activity->id }}"
                                   onclick="event.stopPropagation();">
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                    onclick="event.stopPropagation();">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ route('admin.activities.edit', $activity) }}">
                                    <i class="fas fa-edit"></i> แก้ไข</a></li>
                                <li><a class="dropdown-item" href="#" onclick="togglePublish({{ $activity->id }}, {{ $activity->is_published ? 'false' : 'true' }})">
                                    <i class="fas fa-{{ $activity->is_published ? 'eye-slash' : 'eye' }}"></i> 
                                    {{ $activity->is_published ? 'ยกเลิกการเผยแพร่' : 'เผยแพร่' }}</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteActivity({{ $activity->id }})">
                                    <i class="fas fa-trash"></i> ลบ</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Cover Image -->
                    <div class="position-relative">
                        @if($activity->cover_image)
                            <img src="{{ $activity->cover_image_url }}" class="card-img-top" 
                                 style="height: 200px; object-fit: cover;" alt="{{ $activity->title }}">
                        @else
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                 style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        @endif
                        
                        <!-- Status Badge -->
                        <span class="position-absolute top-0 end-0 m-2">
                            @if($activity->is_published)
                                <span class="badge bg-success">เผยแพร่แล้ว</span>
                            @else
                                <span class="badge bg-warning">ร่าง</span>
                            @endif
                        </span>

                        <!-- Category Badge -->
                        @if($activity->category)
                            <span class="position-absolute top-0 start-0 m-2">
                                <span class="badge" style="background-color: {{ $activity->category->color }};">
                                    {{ $activity->category->name }}
                                </span>
                            </span>
                        @endif
                    </div>

                    <div class="card-body">
                        <h5 class="card-title">{{ $activity->title }}</h5>
                        <p class="card-text text-muted">{{ Str::limit($activity->description, 100) }}</p>
                        
                        <div class="row text-muted small">
                            <div class="col-6">
                                <i class="fas fa-calendar"></i> 
                                {{ $activity->activity_date ? $activity->activity_date->format('d/m/Y') : 'ไม่ระบุ' }}
                            </div>
                            <div class="col-6">
                                <i class="fas fa-images"></i> 
                                {{ $activity->images->count() }} รูป
                            </div>
                        </div>
                        
                        @if($activity->location)
                            <div class="text-muted small mt-1">
                                <i class="fas fa-map-marker-alt"></i> {{ $activity->location }}
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">ยังไม่มีกิจกรรม</h5>
                    <p class="text-muted">เริ่มต้นสร้างกิจกรรมแรกของคุณ</p>
                    <a href="{{ route('admin.activities.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
                    </a>
                </div>
            </div>
        @endforelse
    </div>
</div>



@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Initialize
    updateSelectedCount();



    // Checkbox functionality
    $(document).on('change', '.activity-checkbox', function() {
        updateSelectedCount();
    });

    // Double click to edit
    $('.activity-card').on('dblclick', function(e) {
        e.preventDefault();
        let activityId = $(this).closest('.activity-item').data('activity-id');
        window.location.href = `{{ url('admin/activities') }}/${activityId}/edit`;
    });


});



function updateSelectedCount() {
    let selectedCount = $('.activity-checkbox:checked').length;
    $('#selectedCount').text(selectedCount);

    if (selectedCount > 0) {
        $('#bulkActionsCard').show();
    } else {
        $('#bulkActionsCard').hide();
    }
}



function togglePublish(activityId, isPublished) {
    $.ajax({
        url: `{{ url('admin/activities') }}/${activityId}/toggle-publish`,
        type: 'POST',
        data: {
            _token: '{{ csrf_token() }}',
            is_published: isPublished
        },
        success: function(response) {
            if (response.success) {
                Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                    location.reload();
                });
            } else {
                Swal.fire('ข้อผิดพลาด', response.message, 'error');
            }
        },
        error: function(xhr) {
            Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะ', 'error');
        }
    });
}

function deleteActivity(activityId) {
    Swal.fire({
        title: 'ลบกิจกรรม?',
        text: 'คุณแน่ใจหรือไม่ที่จะลบกิจกรรมนี้? การดำเนินการนี้ไม่สามารถยกเลิกได้',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'ลบเลย',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `{{ url('admin/activities') }}/${activityId}`,
                type: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('ข้อผิดพลาด', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการลบกิจกรรม', 'error');
                }
            });
        }
    });
}

function bulkDelete() {
    let selectedIds = $('.activity-checkbox:checked').map(function() {
        return $(this).val();
    }).get();

    if (selectedIds.length === 0) {
        Swal.fire('แจ้งเตือน', 'กรุณาเลือกกิจกรรมที่ต้องการลบ', 'warning');
        return;
    }

    Swal.fire({
        title: 'ลบกิจกรรมที่เลือก?',
        text: `คุณแน่ใจหรือไม่ที่จะลบกิจกรรม ${selectedIds.length} รายการ? การดำเนินการนี้ไม่สามารถยกเลิกได้`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'ลบเลย',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '{{ route('admin.activities.bulk-delete') }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    activity_ids: selectedIds
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('ข้อผิดพลาด', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการลบกิจกรรม', 'error');
                }
            });
        }
    });
}


</script>
@endsection
