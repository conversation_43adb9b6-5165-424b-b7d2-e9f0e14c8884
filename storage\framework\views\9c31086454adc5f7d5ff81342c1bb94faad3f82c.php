<?php $__env->startSection('title', 'จัดการกิจกรรม'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header Section -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-calendar-alt"></i> จัดการกิจกรรม
            </h1>
            <p class="text-muted mb-0">จัดการข้อมูลกิจกรรมทั้งหมด</p>
        </div>
        <a href="<?php echo e(route('admin.activities.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">กิจกรรมทั้งหมด</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($activities->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">เผยแพร่แล้ว</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($activities->where('is_published', true)->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">ร่าง</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($activities->where('is_published', false)->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-edit fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">หมวดหมู่</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($categories->count()); ?></div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bulk Actions -->
    <div class="card shadow mb-4" id="bulkActionsCard" style="display: none;">
        <div class="card-body">
            <div class="d-flex align-items-center">
                <span class="me-3">
                    <strong id="selectedCount">0</strong> รายการที่เลือก
                </span>
                <button type="button" class="btn btn-danger btn-sm" onclick="bulkDelete()">
                    <i class="fas fa-trash"></i> ลบที่เลือก
                </button>
            </div>
        </div>
    </div>

    <!-- Activities Grid -->
    <div class="row" id="activitiesGrid">
        <?php $__empty_1 = true; $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="col-lg-4 col-md-6 col-12 mb-4 activity-item" 
                 data-activity-id="<?php echo e($activity->id); ?>"
                 data-category-id="<?php echo e($activity->category_id); ?>"
                 data-status="<?php echo e($activity->is_published ? '1' : '0'); ?>">
                <div class="card shadow-sm border-0 h-100 activity-card" style="cursor: pointer;">
                    <div class="card-header bg-white border-0 d-flex justify-content-between align-items-center">
                        <div class="form-check">
                            <input class="form-check-input activity-checkbox" type="checkbox" value="<?php echo e($activity->id); ?>"
                                   onclick="event.stopPropagation();">
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown"
                                    onclick="event.stopPropagation();">
                                <i class="fas fa-ellipsis-v"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo e(route('admin.activities.edit', $activity)); ?>">
                                    <i class="fas fa-edit"></i> แก้ไข</a></li>
                                <li><a class="dropdown-item" href="#" onclick="togglePublish(<?php echo e($activity->id); ?>, <?php echo e($activity->is_published ? 'false' : 'true'); ?>)">
                                    <i class="fas fa-<?php echo e($activity->is_published ? 'eye-slash' : 'eye'); ?>"></i> 
                                    <?php echo e($activity->is_published ? 'ยกเลิกการเผยแพร่' : 'เผยแพร่'); ?></a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#" onclick="deleteActivity(<?php echo e($activity->id); ?>)">
                                    <i class="fas fa-trash"></i> ลบ</a></li>
                            </ul>
                        </div>
                    </div>

                    <!-- Cover Image -->
                    <div class="position-relative">
                        <?php if($activity->cover_image): ?>
                            <img src="<?php echo e($activity->cover_image_url); ?>" class="card-img-top" 
                                 style="height: 200px; object-fit: cover;" alt="<?php echo e($activity->title); ?>">
                        <?php else: ?>
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" 
                                 style="height: 200px;">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Status Badge -->
                        <span class="position-absolute top-0 end-0 m-2">
                            <?php if($activity->is_published): ?>
                                <span class="badge bg-success">เผยแพร่แล้ว</span>
                            <?php else: ?>
                                <span class="badge bg-warning">ร่าง</span>
                            <?php endif; ?>
                        </span>

                        <!-- Category Badge -->
                        <?php if($activity->category): ?>
                            <span class="position-absolute top-0 start-0 m-2">
                                <span class="badge" style="background-color: <?php echo e($activity->category->color); ?>;">
                                    <?php echo e($activity->category->name); ?>

                                </span>
                            </span>
                        <?php endif; ?>
                    </div>

                    <div class="card-body">
                        <h5 class="card-title"><?php echo e($activity->title); ?></h5>
                        <p class="card-text text-muted"><?php echo e(Str::limit($activity->description, 100)); ?></p>
                        
                        <div class="row text-muted small">
                            <div class="col-6">
                                <i class="fas fa-calendar"></i> 
                                <?php echo e($activity->activity_date ? $activity->activity_date->format('d/m/Y') : 'ไม่ระบุ'); ?>

                            </div>
                            <div class="col-6">
                                <i class="fas fa-images"></i> 
                                <?php echo e($activity->images->count()); ?> รูป
                            </div>
                        </div>
                        
                        <?php if($activity->location): ?>
                            <div class="text-muted small mt-1">
                                <i class="fas fa-map-marker-alt"></i> <?php echo e($activity->location); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">ยังไม่มีกิจกรรม</h5>
                    <p class="text-muted">เริ่มต้นสร้างกิจกรรมแรกของคุณ</p>
                    <a href="<?php echo e(route('admin.activities.create')); ?>" class="btn btn-primary">
                        <i class="fas fa-plus"></i> เพิ่มกิจกรรมใหม่
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>



<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
$(document).ready(function() {
    // Initialize
    updateSelectedCount();



    // Checkbox functionality
    $(document).on('change', '.activity-checkbox', function() {
        updateSelectedCount();
    });

    // Double click to edit
    $('.activity-card').on('dblclick', function(e) {
        e.preventDefault();
        let activityId = $(this).closest('.activity-item').data('activity-id');
        window.location.href = `<?php echo e(url('admin/activities')); ?>/${activityId}/edit`;
    });


});



function updateSelectedCount() {
    let selectedCount = $('.activity-checkbox:checked').length;
    $('#selectedCount').text(selectedCount);

    if (selectedCount > 0) {
        $('#bulkActionsCard').show();
    } else {
        $('#bulkActionsCard').hide();
    }
}



function togglePublish(activityId, isPublished) {
    $.ajax({
        url: `<?php echo e(url('admin/activities')); ?>/${activityId}/toggle-publish`,
        type: 'POST',
        data: {
            _token: '<?php echo e(csrf_token()); ?>',
            is_published: isPublished
        },
        success: function(response) {
            if (response.success) {
                Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                    location.reload();
                });
            } else {
                Swal.fire('ข้อผิดพลาด', response.message, 'error');
            }
        },
        error: function(xhr) {
            Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการเปลี่ยนสถานะ', 'error');
        }
    });
}

function deleteActivity(activityId) {
    Swal.fire({
        title: 'ลบกิจกรรม?',
        text: 'คุณแน่ใจหรือไม่ที่จะลบกิจกรรมนี้? การดำเนินการนี้ไม่สามารถยกเลิกได้',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'ลบเลย',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: `<?php echo e(url('admin/activities')); ?>/${activityId}`,
                type: 'DELETE',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>'
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('ข้อผิดพลาด', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการลบกิจกรรม', 'error');
                }
            });
        }
    });
}

function bulkDelete() {
    let selectedIds = $('.activity-checkbox:checked').map(function() {
        return $(this).val();
    }).get();

    if (selectedIds.length === 0) {
        Swal.fire('แจ้งเตือน', 'กรุณาเลือกกิจกรรมที่ต้องการลบ', 'warning');
        return;
    }

    Swal.fire({
        title: 'ลบกิจกรรมที่เลือก?',
        text: `คุณแน่ใจหรือไม่ที่จะลบกิจกรรม ${selectedIds.length} รายการ? การดำเนินการนี้ไม่สามารถยกเลิกได้`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'ลบเลย',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '<?php echo e(route('admin.activities.bulk-delete')); ?>',
                type: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    activity_ids: selectedIds
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire('สำเร็จ', response.message, 'success').then(() => {
                            location.reload();
                        });
                    } else {
                        Swal.fire('ข้อผิดพลาด', response.message, 'error');
                    }
                },
                error: function(xhr) {
                    Swal.fire('ข้อผิดพลาด', 'เกิดข้อผิดพลาดในการลบกิจกรรม', 'error');
                }
            });
        }
    });
}


</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\SoloShop\resources\views/admin/activities/index.blade.php ENDPATH**/ ?>